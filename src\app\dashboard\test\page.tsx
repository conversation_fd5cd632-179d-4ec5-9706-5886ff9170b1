// src/app/dashboard/test/page.tsx
'use client';

import { Loading } from '../../../components/ui/loading';

export default function TestDashboardPage() {
  console.log('TestDashboardPage rendering...');

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Dashboard Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Students</h2>
            <p className="text-3xl font-bold text-blue-600">1,234</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Staff</h2>
            <p className="text-3xl font-bold text-green-600">85</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Classes</h2>
            <p className="text-3xl font-bold text-purple-600">42</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Loading Component Test</h2>
          <div className="h-96 border border-gray-200 rounded">
            <Loading 
              variant="branded"
              showProgress={true}
              size="md"
              fullScreen={false}
              message="Testing loading component..."
            />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Navigation Test</h2>
          <div className="space-y-2">
            <button 
              onClick={() => window.location.href = '/dashboard'}
              className="block w-full text-left px-4 py-2 bg-blue-100 hover:bg-blue-200 rounded"
            >
              Go to Main Dashboard
            </button>
            <button 
              onClick={() => window.location.href = '/auth'}
              className="block w-full text-left px-4 py-2 bg-green-100 hover:bg-green-200 rounded"
            >
              Go to Auth Page
            </button>
            <button 
              onClick={() => window.location.href = '/'}
              className="block w-full text-left px-4 py-2 bg-purple-100 hover:bg-purple-200 rounded"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
