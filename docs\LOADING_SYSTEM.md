# EduPro Loading System Documentation

## Overview
The EduPro loading system provides a consistent, branded loading experience across the entire application. The system features a creative, animated loading component that showcases the EduPro brand while providing meaningful feedback to users.

## Components

### Loading Component (`src/components/ui/loading.tsx`)

A highly customizable loading component that supports multiple variants and configurations.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `message` | string | "Loading..." | Custom loading message |
| `showProgress` | boolean | true | Whether to show progress bar and dynamic messages |
| `size` | 'sm' \| 'md' \| 'lg' | 'md' | Size of the loading animation |
| `variant` | 'default' \| 'minimal' \| 'branded' | 'branded' | Loading variant style |
| `fullScreen` | boolean | true | Whether to render as full-screen overlay |

#### Variants

1. **Branded (Default)**: Complete EduPro branded experience with:
   - Animated EduPro logo with rotating rings
   - Progress bar with EduPro branding
   - Dynamic loading messages
   - Educational theme and inspiration quotes

2. **Default**: Standard loading with simple spinner and message

3. **Minimal**: Minimal spinner for inline use

## Usage Examples

### App-Level Loading (`src/app/loading.tsx`)
```tsx
import { Loading } from '@/components/ui/loading';

export default function AppLoading() {
  return (
    <Loading 
      variant="branded"
      showProgress={true}
      size="lg"
      fullScreen={true}
      message="Loading EduPro..."
    />
  );
}
```

### Page-Level Loading (if needed for specific customization)
```tsx
// Note: Generally not needed as app-level loading handles all routes
// Only create route-specific loading files if you need custom messages
import { Loading } from '@/components/ui/loading';

export default function CustomPageLoading() {
  return (
    <Loading 
      variant="branded"
      showProgress={true}
      size="md"
      fullScreen={true}
      message="Loading custom page content..."
    />
  );
}
```

### Inline Loading (within components)
```tsx
import { Loading } from '@/components/ui/loading';

// Minimal inline loading
<Loading variant="minimal" fullScreen={false} />

// Standard inline loading
<Loading 
  variant="default" 
  fullScreen={false} 
  message="Loading data..." 
  showProgress={false}
/>
```

## Features

### Animations
- **Logo Animation**: Rotating rings and pulsing effects around the EduPro logo
- **Progress Bar**: Animated progress with shimmer effects
- **Loading Dots**: Bouncing dots animation
- **Dynamic Messages**: Contextual loading messages that change based on progress

### Branding Elements
- **EduPro Logo**: Prominent display with "EP" initials
- **Brand Colors**: Consistent emerald and teal color scheme
- **Typography**: Brand-consistent fonts and sizing
- **Inspirational Content**: Educational quotes and mission statement

### Responsiveness
- Fully responsive design that works on all screen sizes
- Adaptive sizing based on screen dimensions
- Touch-friendly on mobile devices

## File Structure

```
src/
├── app/
│   └── loading.tsx                    # Global app-level loading page
├── components/
│   └── ui/
│       ├── loading.tsx                # Main loading component
│       └── index.ts                   # UI components export
└── globals.css                        # Custom animations and styles
```

**Note**: Route-specific loading files are no longer needed as the global `src/app/loading.tsx` handles loading states for all routes. Only create route-specific loading files if you need highly customized loading messages for specific sections.

## Custom Animations

The following custom animations are defined in `src/globals.css`:

- `@keyframes shimmer`: Shimmer effect for progress bars
- `@keyframes spin-slow`: Slow rotation animation
- `@keyframes spin-reverse`: Reverse rotation animation
- `@keyframes gradient`: Gradient color animation

## Integration Guidelines

### For New Pages
The global `src/app/loading.tsx` file handles loading states for all routes automatically. You typically don't need to create route-specific loading files unless you need highly customized loading messages.

### For Route-Specific Loading (Optional)
1. Only create if you need custom loading messages for specific routes
2. Create a `loading.tsx` file in the specific route directory
3. Import and use the Loading component with route-specific props
4. Customize the message to match the route context

### For Existing Components
1. Replace existing loading spinners with the new Loading component
2. Use the `minimal` or `default` variant for inline usage
3. Set `fullScreen={false}` for non-overlay usage

### Best Practices
1. Rely on the global loading file for most use cases
2. Use the `branded` variant for page-level loading
3. Use contextual messages that describe what's being loaded
4. Consider using `showProgress={false}` for quick operations
5. Use appropriate sizing based on the container
6. Test loading states across different screen sizes

## Accessibility Features

- Proper ARIA labels for screen readers
- High contrast colors for better visibility
- Smooth animations that respect user motion preferences
- Keyboard navigation support where applicable

## Customization

The loading component can be easily customized by:
1. Modifying the brand colors in the component
2. Adding new variants to the component
3. Extending the loading messages array
4. Adding custom animations to globals.css

## Performance Considerations

- Animations are optimized for performance
- Progress simulation is resource-efficient
- Component is lazy-loaded where possible
- Minimal JavaScript footprint
