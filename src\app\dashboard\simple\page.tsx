// src/app/dashboard/simple/page.tsx
'use client';

import { useEffect, useState } from 'react';

export default function SimpleDashboardPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    console.log('SimpleDashboardPage mounted');
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Mounting...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="max-w-4xl mx-auto p-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Simple Dashboard</h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-100 border border-green-300 rounded">
              <p className="text-green-800">✅ Dashboard page is rendering successfully!</p>
            </div>
            
            <div className="p-4 bg-blue-100 border border-blue-300 rounded">
              <p className="text-blue-800">🔍 This is a simplified version without auth or complex components</p>
            </div>
            
            <div className="p-4 bg-yellow-100 border border-yellow-300 rounded">
              <p className="text-yellow-800">⚠️ If you see this, the issue is likely in the auth flow or complex components</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-semibold text-blue-900">Students</h3>
                <p className="text-2xl font-bold text-blue-600">1,234</p>
              </div>
              <div className="bg-green-50 p-4 rounded">
                <h3 className="font-semibold text-green-900">Staff</h3>
                <p className="text-2xl font-bold text-green-600">85</p>
              </div>
              <div className="bg-purple-50 p-4 rounded">
                <h3 className="font-semibold text-purple-900">Classes</h3>
                <p className="text-2xl font-bold text-purple-600">42</p>
              </div>
            </div>

            <div className="mt-8 space-x-4">
              <button 
                onClick={() => window.location.href = '/dashboard'}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Try Main Dashboard
              </button>
              <button 
                onClick={() => window.location.href = '/auth'}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Go to Auth
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
