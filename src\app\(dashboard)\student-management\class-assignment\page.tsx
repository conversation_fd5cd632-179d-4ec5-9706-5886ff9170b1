// src/app/student-management/class-assignment/page.tsx
'use client';

import { AppLayout } from '../../../components/layout';

export default function ClassAssignmentPage() {
  return (
    <AppLayout title="Class Assignment">
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Class Assignment</h1>
            <p className="text-gray-600 dark:text-gray-400">Assign students to classes and manage class rosters</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="text-center py-12">
              <div className="mx-auto h-24 w-24 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Class Assignment Management</h3>
              <p className="text-gray-500 dark:text-gray-400">This page will handle student class assignments and roster management.</p>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
