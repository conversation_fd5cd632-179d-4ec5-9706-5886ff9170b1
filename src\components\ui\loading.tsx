'use client';

import { useEffect, useState } from 'react';

interface LoadingProps {
  message?: string;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'branded';
  fullScreen?: boolean;
}

export const Loading = ({ 
  message = "Loading...", 
  showProgress = true, 
  size = 'md',
  variant = 'branded',
  fullScreen = true
}: LoadingProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('Initializing EduPro');

  const loadingMessages = [
    'Initializing EduPro...',
    'Loading your workspace...',
    'Preparing student data...',
    'Setting up dashboard...',
    'Finalizing setup...',
    'Almost ready!'
  ];

  useEffect(() => {
    if (!showProgress) return;
    
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 12 + 3;
        const messageIndex = Math.floor((newProgress / 100) * loadingMessages.length);
        setLoadingText(loadingMessages[Math.min(messageIndex, loadingMessages.length - 1)]);
        return Math.min(newProgress, 95); // Stop at 95% to avoid completing before actual load
      });
    }, 150);

    return () => clearInterval(interval);
  }, [showProgress]);

  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-20 h-20',
    lg: 'w-28 h-28'
  };

  const containerClasses = fullScreen 
    ? "fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex items-center justify-center z-50"
    : "flex items-center justify-center p-8";

  if (variant === 'minimal') {
    return (
      <div className={containerClasses}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (variant === 'default') {
    return (
      <div className={containerClasses}>
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 text-sm">{message}</p>
        </div>
      </div>
    );
  }

  // Branded variant (default)
  return (
    <div className={containerClasses}>
      <div className="text-center space-y-8 max-w-md mx-auto px-6">
        {/* Logo and Brand */}
        <div className="flex flex-col items-center space-y-6">
          <div className="relative">
            {/* Animated logo container */}
            <div className={`${sizeClasses[size]} bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-emerald-500/40 relative overflow-hidden`}>
              <span className="text-white font-bold text-xl sm:text-2xl lg:text-3xl z-10">EP</span>
              
              {/* Animated background pulse */}
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-2xl animate-pulse opacity-70"></div>
              
              {/* Rotating gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-2xl animate-spin-slow"></div>
            </div>
            
            {/* Spinning rings around logo */}
            <div className={`absolute inset-0 ${sizeClasses[size]} border-3 border-transparent border-t-emerald-500 border-r-emerald-400 rounded-2xl animate-spin`}></div>
            <div className={`absolute inset-0 ${sizeClasses[size]} border-2 border-transparent border-b-teal-500 border-l-teal-400 rounded-2xl animate-spin-reverse`}></div>
          </div>
          
          <div className="space-y-3">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-emerald-700 bg-clip-text text-transparent animate-gradient">
              EduPro
            </h1>
            <p className="text-sm text-gray-500 font-medium tracking-wide">Education Management System</p>
          </div>
        </div>

        {/* Progress Section */}
        {showProgress && (
          <div className="space-y-6 w-full">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">{loadingText}</span>
                <span className="text-sm text-emerald-600 font-semibold">{Math.round(progress)}%</span>
              </div>
              
              {/* Creative Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden shadow-inner relative">
                <div 
                  className="h-full bg-gradient-to-r from-emerald-500 via-teal-500 to-emerald-600 transition-all duration-500 ease-out rounded-full shadow-lg relative overflow-hidden"
                  style={{ width: `${Math.min(progress, 100)}%` }}
                >
                  {/* Shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
                  
                  {/* EduPro text in progress bar */}
                  {progress > 30 && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-bold text-white/90 tracking-wider">EDUPRO</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Animated loading dots */}
            <div className="flex justify-center space-x-2">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce shadow-lg"
                  style={{ 
                    animationDelay: `${i * 0.15}s`,
                    animationDuration: '1s'
                  }}
                ></div>
              ))}
            </div>
          </div>
        )}

        {/* Inspirational content */}
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center space-x-3">
            <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-emerald-400"></div>
            <p className="text-xs text-gray-400 italic font-medium">
              "Empowering Education Through Technology"
            </p>
            <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-emerald-400"></div>
          </div>
          
          <div className="flex items-center justify-center space-x-3 text-xs text-gray-400">
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <div 
                  key={i}
                  className="w-1 h-1 bg-emerald-400 rounded-full animate-ping" 
                  style={{ animationDelay: `${i * 0.3}s` }}
                ></div>
              ))}
            </div>
            <span className="tracking-wide">Connecting Students • Teachers • Learning</span>
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <div 
                  key={i}
                  className="w-1 h-1 bg-teal-400 rounded-full animate-ping" 
                  style={{ animationDelay: `${i * 0.3 + 0.15}s` }}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Loading;
